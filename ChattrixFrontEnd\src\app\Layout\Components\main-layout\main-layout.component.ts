import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Subject, takeUntil } from 'rxjs';
import { AuthStateService } from '../../../Pages/authentication/Services/AuthState.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-main-layout',
  standalone: false,
  templateUrl: './main-layout.component.html',
  styleUrl: './main-layout.component.scss'
})
export class MainLayoutComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  isLoading = false;
  isAuthenticated = false;

  constructor(
    private authState: AuthStateService,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Subscribe to authentication state
    this.authState.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe((state) => {
        this.isLoading = state.isLoading;
        this.isAuthenticated = state.isAuthenticated;

        // Redirect to login if not authenticated
        if (!this.isLoading && !this.isAuthenticated) {
          this.router.navigate(['/auth/login']);
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
