import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from '../authentication/Guards/auth.guard';
import { MainLayoutComponent } from '../../Layout/Components/main-layout/main-layout.component';
import { UserListComponent } from './Pages/user-list/user-list.component';

const routes: Routes = [
  {
    path: '',
    component: MainLayoutComponent,
    canActivate: [AuthGuard],
    children: [
      {
        path: '',
        redirectTo: 'list',
        pathMatch: 'full',
      },
      {
        path: 'list',
        component: UserListComponent,
        title: 'User Management - Chattrix',
      },
      // Future routes for user details, add/edit user, etc.
      // {
      //   path: 'add',
      //   component: AddEditUserComponent,
      //   title: 'Add User - Chattrix',
      // },
      // {
      //   path: 'edit/:id',
      //   component: AddEditUserComponent,
      //   title: 'Edit User - Chattrix',
      // },
      // {
      //   path: 'details/:id',
      //   component: UserDetailsComponent,
      //   title: 'User Details - Chattrix',
      // },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class UserManagementRoutingModule {}
