import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { JwtInterceptor } from './Core/Interceptors/jwt.interceptor';
import { UserListComponent } from './Pages/user-management/Pages/user-list/user-list.component';
import { AddEditUserComponent } from './Pages/user-management/Pages/add-edit-user/add-edit-user.component';
import { UserDetailsComponent } from './Pages/user-management/Pages/user-details/user-details.component';

@NgModule({
  declarations: [AppComponent, UserListComponent, AddEditUserComponent, UserDetailsComponent],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    HttpClientModule,
    AppRoutingModule,
  ],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: JwtInterceptor,
      multi: true,
    },
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
