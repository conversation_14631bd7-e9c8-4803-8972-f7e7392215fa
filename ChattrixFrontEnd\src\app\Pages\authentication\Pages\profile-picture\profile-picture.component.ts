import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { MatSnackBar } from '@angular/material/snack-bar';

import { AuthenticationService } from '../../Services/Authentication.service';
import { AuthStateService } from '../../Services/AuthState.service';
import { FileUploadService } from '../../Services/FileUpload.service';
import { RegisterRequest, AuthError, UserInfo } from '../../Models';

@Component({
  selector: 'app-profile-picture',
  standalone: false,
  templateUrl: './profile-picture.component.html',
  styleUrl: './profile-picture.component.scss',
})
export class ProfilePictureComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  profileForm: FormGroup;
  logoLoaded = true;
  selectedFile: File | null = null;
  imagePreview: string | null = null;
  isLoading = false;

  // File validation
  maxFileSize = 5 * 1024 * 1024; // 5MB
  allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];

  constructor(
    private authService: AuthenticationService,
    private authState: AuthStateService,
    private fileUploadService: FileUploadService,
    private formBuilder: FormBuilder,
    private router: Router,
    private snackBar: MatSnackBar,
  ) {
    this.profileForm = this.formBuilder.group({
      description: ['', [Validators.maxLength(500)]],
    });
  }

  ngOnInit(): void {
    // Check if we have pending signup data
    const pendingData = sessionStorage.getItem('pendingSignupData');
    if (!pendingData) {
      // If no pending data, redirect to signup
      this.router.navigate(['/auth/signup']);
      return;
    }

    // Subscribe to auth state changes
    this.authState.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe((state) => {
        this.isLoading = state.isLoading;
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Validate file using service
      const validation = this.fileUploadService.validateFile(file);
      if (!validation.isValid) {
        this.showError(validation.error!);
        return;
      }

      this.selectedFile = file;

      // Create image preview using service
      this.fileUploadService
        .createImagePreview(file)
        .then((preview) => {
          this.imagePreview = preview;
        })
        .catch((error) => {
          console.error('Error creating preview:', error);
          this.showError('Failed to create image preview.');
        });
    }
  }

  removeImage(): void {
    this.selectedFile = null;
    this.imagePreview = null;
    // Reset file input
    const fileInput = document.getElementById('fileInput') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  }

  triggerFileInput(): void {
    const fileInput = document.getElementById('fileInput') as HTMLInputElement;
    if (fileInput) {
      fileInput.click();
    }
  }

  onCompleteProfile(): void {
    if (this.profileForm.valid) {
      const pendingData = sessionStorage.getItem('pendingSignupData');
      if (!pendingData) {
        this.showError('Session expired. Please start over.');
        this.router.navigate(['/auth/signup']);
        return;
      }

      const signupData = JSON.parse(pendingData);

      // Create FormData using service
      const formData = this.fileUploadService.prepareRegistrationFormData(
        signupData,
        this.profileForm.value.description || '',
        this.selectedFile || undefined,
      );

      this.isLoading = true;

      // Call the registration API with FormData
      this.registerWithProfile(formData);
    } else {
      this.markFormGroupTouched();
    }
  }

  private registerWithProfile(formData: FormData): void {
    this.fileUploadService.registerWithProfile(formData).subscribe({
      next: (response) => {
        if (response.isSuccess) {
          // Clear pending data
          sessionStorage.removeItem('pendingSignupData');

          this.showSuccess(
            'Account created successfully! Please check your email for verification.',
          );

          // Navigate to OTP verification if required
          if (response.data?.userId) {
            this.router.navigate(['/auth/otp-verification'], {
              queryParams: { userId: response.data.userId },
            });
          } else {
            this.router.navigate(['/auth/login']);
          }
        }
      },
      error: (error: AuthError) => {
        console.error('Registration failed:', error);
        this.isLoading = false;
      },
    });
  }

  onBack(): void {
    this.router.navigate(['/auth/signup']);
  }

  onImageError(event: any): void {
    this.logoLoaded = false;
    console.log('Logo failed to load, showing fallback');
  }

  private markFormGroupTouched(): void {
    Object.keys(this.profileForm.controls).forEach((key) => {
      const control = this.profileForm.get(key);
      control?.markAsTouched();
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar'],
    });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['success-snackbar'],
    });
  }

  // Getter methods for template
  get descriptionControl() {
    return this.profileForm.get('description');
  }
}
