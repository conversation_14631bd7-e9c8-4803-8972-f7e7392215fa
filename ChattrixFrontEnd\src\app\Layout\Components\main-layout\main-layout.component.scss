/* Main Layout Container */
.main-layout-container {
  display: flex;
  height: 100vh;
  background: var(--bg-primary);
  overflow: hidden;
}

.sidebar {
  flex-shrink: 0;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: var(--bg-primary);
}

/* Loading State */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: var(--bg-primary);
}

.loading-content {
  text-align: center;
  color: var(--text-secondary);
}

.loading-icon {
  font-size: 3rem;
  width: 3rem;
  height: 3rem;
  color: var(--accent-green);
  margin-bottom: var(--spacing-md);
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 1rem;
  font-weight: 500;
  margin: 0;
}

/* Not Authenticated State */
.not-authenticated {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: var(--bg-primary);
}

.error-content {
  text-align: center;
  color: var(--text-secondary);
  max-width: 400px;
  padding: var(--spacing-xl);
}

.error-icon {
  font-size: 4rem;
  width: 4rem;
  height: 4rem;
  color: var(--error);
  margin-bottom: var(--spacing-md);
}

.error-content h2 {
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 var(--spacing-md) 0;
}

.error-content p {
  color: var(--text-secondary);
  font-size: 1rem;
  line-height: 1.5;
  margin: 0 0 var(--spacing-sm) 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-layout-container {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
  }

  .sidebar {
    width: 100%;
    height: auto;
  }

  .main-content {
    flex: 1;
    min-height: calc(100vh - 200px);
  }

  .loading-icon {
    font-size: 2.5rem;
    width: 2.5rem;
    height: 2.5rem;
  }

  .error-icon {
    font-size: 3rem;
    width: 3rem;
    height: 3rem;
  }

  .error-content h2 {
    font-size: 1.25rem;
  }

  .error-content p {
    font-size: 0.9rem;
  }
}

/* Light Theme Overrides */
:host-context(.light-theme) {
  .main-layout-container,
  .main-content,
  .loading-container,
  .not-authenticated {
    background: #ffffff;
  }

  .loading-text {
    color: #666666;
  }

  .error-content h2 {
    color: #333333;
  }

  .error-content p {
    color: #666666;
  }
}
