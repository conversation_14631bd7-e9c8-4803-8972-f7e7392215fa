<div class="main-layout-container" *ngIf="!isLoading && isAuthenticated">
  <!-- Sidebar Navigation -->
  <app-side-bar class="sidebar"></app-side-bar>

  <!-- Main Content Area -->
  <div class="main-content">
    <router-outlet></router-outlet>
  </div>
</div>

<!-- Loading State -->
<div *ngIf="isLoading" class="loading-container">
  <div class="loading-content">
    <mat-icon class="loading-icon">hourglass_empty</mat-icon>
    <p class="loading-text">Loading...</p>
  </div>
</div>

<!-- Not Authenticated State -->
<div *ngIf="!isLoading && !isAuthenticated" class="not-authenticated">
  <div class="error-content">
    <mat-icon class="error-icon">lock</mat-icon>
    <h2>Access Denied</h2>
    <p>You need to be logged in to access this area.</p>
    <p>Redirecting to login...</p>
  </div>
</div>
