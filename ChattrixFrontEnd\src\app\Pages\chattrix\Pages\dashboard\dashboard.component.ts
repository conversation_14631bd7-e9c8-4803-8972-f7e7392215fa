import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';

import { AuthenticationService } from '../../../authentication/Services/Authentication.service';
import { AuthStateService } from '../../../authentication/Services/AuthState.service';
import { UserInfo, AuthState } from '../../../authentication/Models';

@Component({
  selector: 'app-dashboard',
  standalone: false,
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.scss',
})
export class DashboardComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // User state
  user: UserInfo | null = null;
  isLoading = false;
  isAuthenticated = false;

  constructor(
    private authService: AuthenticationService,
    private authState: AuthStateService,
    private router: Router,
  ) {}

  ngOnInit(): void {
    // Subscribe to authentication state
    this.authState.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe((state: AuthState) => {
        this.isAuthenticated = state.isAuthenticated;
        this.isLoading = state.isLoading;
        this.user = state.user;

        // Redirect to login if not authenticated
        if (!state.isAuthenticated) {
          this.router.navigate(['/auth/login']);
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onLogout(): void {
    this.authService.logout();
    // Navigation will be handled by auth service
  }

  // Getter for user display name
  get userDisplayName(): string {
    if (this.user) {
      return this.user.name || this.user.email || 'User';
    }
    return 'User';
  }

  // Getter for user role display
  get userRoleDisplay(): string {
    if (this.user?.role) {
      if (Array.isArray(this.user.role)) {
        return this.user.role.join(', ');
      }
      return this.user.role;
    }
    return 'User';
  }
}
