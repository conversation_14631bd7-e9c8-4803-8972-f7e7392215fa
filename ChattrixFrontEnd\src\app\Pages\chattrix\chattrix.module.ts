import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

// Material UI Modules
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatDividerModule } from '@angular/material/divider';
import { MatMenuModule } from '@angular/material/menu';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatTooltipModule } from '@angular/material/tooltip';

import { ChattrixRoutingModule } from './chattrix-routing.module';
import { UserManagementComponent } from './Pages/user-management/user-management.component';
import { DashboardComponent } from './Pages/dashboard/dashboard.component';
import { SideBarComponent } from './Pages/side-bar/side-bar.component';
import { ChatWindowComponent } from './Pages/chat-window/chat-window.component';
import { ChatUsersComponent } from './Pages/chat-users/chat-users.component';

@NgModule({
  declarations: [
    UserManagementComponent,
    DashboardComponent,
    SideBarComponent,
    ChatWindowComponent,
    ChatUsersComponent,
  ],
  imports: [
    CommonModule,
    ChattrixRoutingModule,
    // Material UI Modules
    MatSidenavModule,
    MatListModule,
    MatIconModule,
    MatButtonModule,
    MatCardModule,
    MatSlideToggleModule,
    MatDividerModule,
    MatMenuModule,
    MatToolbarModule,
    MatTooltipModule,
  ],
})
export class ChattrixModule {}
