/* Dashboard Component Styles */

.dashboard-container {
  display: flex;
  height: 100vh;
  background: var(--bg-primary);
  overflow: hidden;
}

.sidebar {
  flex-shrink: 0;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: var(--bg-primary);
}

.content-placeholder {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
}

.placeholder-content {
  text-align: center;
  color: var(--text-secondary);
  max-width: 400px;
}

.placeholder-icon {
  font-size: 4rem;
  width: 4rem;
  height: 4rem;
  color: var(--text-muted);
  margin-bottom: var(--spacing-md);
}

.placeholder-content h2 {
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 var(--spacing-md) 0;
}

.placeholder-content p {
  color: var(--text-secondary);
  font-size: 1rem;
  line-height: 1.5;
  margin: var(--spacing-sm) 0;
}

/* Loading State */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: var(--bg-primary);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
}

.loading-icon {
  font-size: 3rem;
  width: 3rem;
  height: 3rem;
  color: var(--accent-green);
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  color: var(--text-secondary);
  font-size: var(--font-size-md);
  font-weight: 500;
  margin: 0;
}

/* Not Authenticated State */
.not-authenticated {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: var(--bg-primary);
  padding: var(--spacing-lg);
}

.error-content {
  text-align: center;
  color: var(--text-secondary);
  max-width: 400px;
}

.error-icon {
  font-size: 4rem;
  width: 4rem;
  height: 4rem;
  color: var(--error);
  margin-bottom: var(--spacing-md);
}

.error-content h2 {
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 var(--spacing-md) 0;
}

.error-content p {
  color: var(--text-secondary);
  font-size: 1rem;
  line-height: 1.5;
  margin: var(--spacing-sm) 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-container {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
  }

  .sidebar {
    width: 100%;
    height: auto;
  }

  .main-content {
    flex: 1;
    min-height: calc(100vh - 200px);
  }

  .placeholder-icon {
    font-size: 3rem;
    width: 3rem;
    height: 3rem;
  }

  .placeholder-content h2 {
    font-size: 1.25rem;
  }

  .placeholder-content p {
    font-size: 0.9rem;
  }
}

/* Light Theme Overrides */
:host-context(.light-theme) {
  .dashboard-container,
  .main-content,
  .loading-container,
  .not-authenticated {
    background: #ffffff;
  }

  .placeholder-content h2 {
    color: #333333;
  }

  .placeholder-content p {
    color: #666666;
  }

  .placeholder-icon {
    color: #999999;
  }

  .loading-text {
    color: #666666;
  }

  .error-content h2 {
    color: #333333;
  }

  .error-content p {
    color: #666666;
  }
}
