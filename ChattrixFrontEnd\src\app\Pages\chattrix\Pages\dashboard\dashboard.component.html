<div class="dashboard-container" *ngIf="!isLoading && isAuthenticated">
  <!-- Sidebar Navigation -->
  <app-side-bar class="sidebar"></app-side-bar>

  <!-- Main Content Area -->
  <div class="main-content">
    <!-- Content will be added in future iterations -->
    <div class="content-placeholder">
      <div class="placeholder-content">
        <mat-icon class="placeholder-icon">chat</mat-icon>
        <h2>Welcome to Chattrix</h2>
        <p>
          Chat window and user list will be implemented in the next iteration.
        </p>
        <p>Use the sidebar to navigate to User Management or other features.</p>
      </div>
    </div>
  </div>
</div>

<!-- Loading State -->
<div *ngIf="isLoading" class="loading-container">
  <div class="loading-content">
    <mat-icon class="loading-icon">hourglass_empty</mat-icon>
    <p class="loading-text">Loading dashboard...</p>
  </div>
</div>

<!-- Not Authenticated State -->
<div *ngIf="!isLoading && !isAuthenticated" class="not-authenticated">
  <div class="error-content">
    <mat-icon class="error-icon">lock</mat-icon>
    <h2>Access Denied</h2>
    <p>You need to be logged in to access the dashboard.</p>
    <p>Redirecting to login...</p>
  </div>
</div>
